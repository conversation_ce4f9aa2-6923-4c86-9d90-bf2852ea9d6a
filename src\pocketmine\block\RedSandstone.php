<?php

/*
 *
 *
 *    __    _         _         __   __
 *   |  \  | |_      | |    _  |  \_/  |
 *   |   \ | (_) ___ | |__ | |_|       | ___   ___  ____
 *   | |\ \| | |/ _ \|  _ \| __| |\_/| |/ _ \ / _ \|  _ \
 *   | | \   | | (_| | / \ | |_| |   | | (_) | (_) | | | |
 *   |_|  \__|_|\__  |_| |_|\__|_|   |_|\___/ \___/|_| |_|
 *               __| |
 *              |___/
 *
 *
 *
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * <AUTHOR>
 * @link https://github.com/NightMoonTeam/NightMoon
 *
 *
*/

namespace pocketmine\block;

class RedSandstone extends Sandstone {
	protected $id = Block::RED_SANDSTONE;

	/**
	 * @return string
	 */
	public function getName() : string{
		static $names = [
			0 => "Red Sandstone",
			1 => "Chiseled Red Sandstone",
			2 => "Smooth Red Sandstone",
			3 => "",
		];
		return $names[$this->meta & 0x03];
	}

    public function canHarvestWithHand(): bool{
        return false;
    }
}
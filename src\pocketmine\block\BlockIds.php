<?php

/*
 *
 *
 *    __    _         _         __   __
 *   |  \  | |_      | |    _  |  \_/  |
 *   |   \ | (_) ___ | |__ | |_|       | ___   ___  ____
 *   | |\ \| | |/ _ \|  _ \| __| |\_/| |/ _ \ / _ \|  _ \
 *   | | \   | | (_| | / \ | |_| |   | | (_) | (_) | | | |
 *   |_|  \__|_|\__  |_| |_|\__|_|   |_|\___/ \___/|_| |_|
 *               __| |
 *              |___/
 *
 *
 *
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * <AUTHOR>
 * @link https://github.com/NightMoonTeam/NightMoon
 *
 *
*/

namespace pocketmine\block;

interface BlockIds {

	const AIR = 0;
	const STONE = 1;
	const GRASS = 2;
	const DIRT = 3;
	const COBBLESTONE = 4;
	const COBBLE = 4;
	const PLANK = 5;
	const PLANKS = 5;
	const WOODEN_PLANK = 5;
	const WOODEN_PLANKS = 5;
	const SAPLING = 6;
	const SAPLINGS = 6;
	const BEDROCK = 7;
	const WATER = 8;
	const STILL_WATER = 9;
	const LAVA = 10;
	const STILL_LAVA = 11;
	const SAND = 12;
	const GRAVEL = 13;
	const GOLD_ORE = 14;
	const IRON_ORE = 15;
	const COAL_ORE = 16;
	const LOG = 17;
	const WOOD = 17;
	const TRUNK = 17;
	const LEAVES = 18;
	const SPONGE = 19;
	const GLASS = 20;
	const LAPIS_ORE = 21;
	const LAPIS_BLOCK = 22;
	const DISPENSER = 23;
	const SANDSTONE = 24;
	const NOTEBLOCK = 25;
	const BED_BLOCK = 26;
	const POWERED_RAIL = 27;
	const DETECTOR_RAIL = 28;
	const STICKY_PISTON = 29;
	const COBWEB = 30;
	const TALL_GRASS = 31;
	const BUSH = 32;
	const DEAD_BUSH = 32;
	const PISTON = 33;
	const PISTON_HEAD = 34;
	const WOOL = 35;

	const DANDELION = 37;
	const POPPY = 38;
	const ROSE = 38;
	const RED_FLOWER = 38;
	const BROWN_MUSHROOM = 39;
	const RED_MUSHROOM = 40;
	const GOLD_BLOCK = 41;
	const IRON_BLOCK = 42;
	const DOUBLE_SLAB = 43;
	const DOUBLE_SLABS = 43;
	const SLAB = 44;
	const SLABS = 44;
	const STONE_SLAB = 44;
	const BRICKS = 45;
	const BRICKS_BLOCK = 45;
	const TNT = 46;
	const BOOKSHELF = 47;
	const MOSS_STONE = 48;
	const MOSSY_STONE = 48;
	const OBSIDIAN = 49;
	const TORCH = 50;
	const FIRE = 51;
	const MONSTER_SPAWNER = 52;
	const WOOD_STAIRS = 53;
	const WOODEN_STAIRS = 53;
	const OAK_WOOD_STAIRS = 53;
	const OAK_WOODEN_STAIRS = 53;
	const CHEST = 54;
	const REDSTONE_WIRE = 55;
	const DIAMOND_ORE = 56;
	const DIAMOND_BLOCK = 57;
	const CRAFTING_TABLE = 58;
	const WORKBENCH = 58;
	const WHEAT_BLOCK = 59;
	const FARMLAND = 60;
	const FURNACE = 61;
	const BURNING_FURNACE = 62;
	const LIT_FURNACE = 62;
	const SIGN_POST = 63;
	const DOOR_BLOCK = 64;
	const WOODEN_DOOR_BLOCK = 64;
	const WOOD_DOOR_BLOCK = 64;
	const LADDER = 65;
	const RAIL = 66;
	const COBBLESTONE_STAIRS = 67;
	const COBBLE_STAIRS = 67;
	const WALL_SIGN = 68;
	const LEVER = 69;
	const STONE_PRESSURE_PLATE = 70;
	const IRON_DOOR_BLOCK = 71;
	const WOODEN_PRESSURE_PLATE = 72;
	const REDSTONE_ORE = 73;
	const GLOWING_REDSTONE_ORE = 74;
	const UNLIT_REDSTONE_TORCH = 75;
	const REDSTONE_TORCH = 76;
	const LIT_REDSTONE_TORCH = 76;
	const STONE_BUTTON = 77;
	const SNOW = 78;
	const SNOW_LAYER = 78;
	const ICE = 79;
	const SNOW_BLOCK = 80;
	const CACTUS = 81;
	const CLAY_BLOCK = 82;
	const REEDS = 83;
	const SUGARCANE_BLOCK = 83;

	const FENCE = 85;
	const PUMPKIN = 86;
	const NETHERRACK = 87;
	const SOUL_SAND = 88;
	const GLOWSTONE = 89;
	const GLOWSTONE_BLOCK = 89;
	const PORTAL_BLOCK = 90;
	const PORTAL = 90;
	const JACK_O_LANTERN = 91;
	const LIT_PUMPKIN = 91;
	const CAKE_BLOCK = 92;
	const UNPOWERED_REPEATER_BLOCK = 93;
	const POWERED_REPEATER_BLOCK = 94;
	const INVISIBLE_BEDROCK = 95;
	const TRAPDOOR = 96;
	const WOODEN_TRAPDOOR = 96;
	const MONSTER_EGG_BLOCK = 97;
	const STONE_BRICKS = 98;
	const STONE_BRICK = 98;
	const BROWN_MUSHROOM_BLOCK = 99;
	const RED_MUSHROOM_BLOCK = 100;
	const IRON_BARS = 101;
	const IRON_BAR = 101;
	const GLASS_PANE = 102;
	const GLASS_PANEL = 102;
	const MELON_BLOCK = 103;
	const PUMPKIN_STEM = 104;
	const MELON_STEM = 105;
	const VINES = 106;
	const VINE = 106;
	const FENCE_GATE = 107;
	const OAK_FENCE_GATE = 107;
	const BRICK_STAIRS = 108;
	const STONE_BRICK_STAIRS = 109;
	const MYCELIUM = 110;
	const LILY_PAD = 111;
	const WATER_LILY = 111;
	const NETHER_BRICKS = 112;
	const NETHER_BRICK_BLOCK = 112;
	const NETHER_BRICK_FENCE = 113;
	const NETHER_BRICK_STAIRS = 114;
	const NETHER_BRICKS_STAIRS = 114;
	const NETHER_WART_BLOCK = 115;
	const ENCHANTING_TABLE = 116;
	const ENCHANT_TABLE = 116;
	const ENCHANTMENT_TABLE = 116;
	const BREWING_STAND_BLOCK = 117;
	const CAULDRON_BLOCK = 118;
	const END_PORTAL = 119; //EndPortal is 119 not 120 :)
	const END_PORTAL_FRAME = 120;
	const END_STONE = 121;
	const DRAGON_EGG = 122;
	const REDSTONE_LAMP = 123, INACTIVE_REDSTONE_LAMP = 123;
	const LIT_REDSTONE_LAMP = 124, ACTIVE_REDSTONE_LAMP = 124;
	const DROPPER = 125;
	const ACTIVATOR_RAIL = 126;
	const COCOA_BLOCK = 127;
	const COCOA_PODS = 127;
	const SANDSTONE_STAIRS = 128;
	const EMERALD_ORE = 129;
	const ENDER_CHEST = 130;
	const TRIPWIRE_HOOK = 131;
	const TRIPWIRE = 132;
	const EMERALD_BLOCK = 133;
	const SPRUCE_WOOD_STAIRS = 134;
	const SPRUCE_WOODEN_STAIRS = 134;
	const BIRCH_WOOD_STAIRS = 135;
	const BIRCH_WOODEN_STAIRS = 135;
	const JUNGLE_WOOD_STAIRS = 136;
	const JUNGLE_WOODEN_STAIRS = 136;
	const COMMAND_BLOCK = 137;
	const BEACON = 138;
	const COBBLESTONE_WALL = 139;
	const COBBLE_WALL = 139;
	const STONE_WALL = 139;
	const FLOWER_POT_BLOCK = 140;
	const CARROT_BLOCK = 141;
	const POTATO_BLOCK = 142;
	const WOODEN_BUTTON = 143;
	const MOB_HEAD_BLOCK = 144;
	const SKULL_BLOCK = 144;
	const ANVIL = 145;
	const TRAPPED_CHEST = 146;
	const WEIGHTED_PRESSURE_PLATE_LIGHT = 147;
	const LIGHT_WEIGHTED_PRESSURE_PLATE = 147;
	const GOLD_PRESSURE_PLATE = 147;
	const WEIGHTED_PRESSURE_PLATE_HEAVY = 148;
	const HEAVY_WEIGHTED_PRESSURE_PLATE = 148;
	const IRON_PRESSURE_PLATE = 148;
	const COMPARATOR_BLOCK = 149;
	const UNPOWERED_COMPARATOR_BLOCK = 149;
	const POWERED_COMPARATOR_BLOCK = 150;
	const DAYLIGHT_SENSOR = 151;
	const REDSTONE_BLOCK = 152;
	const NETHER_QUARTZ_ORE = 153;
	const HOPPER_BLOCK = 154;
	const QUARTZ_BLOCK = 155;
	const QUARTZ_STAIRS = 156;
	const DOUBLE_WOOD_SLAB = 157;
	const DOUBLE_WOODEN_SLAB = 157;
	const DOUBLE_WOOD_SLABS = 157;
	const DOUBLE_WOODEN_SLABS = 157;
	const WOOD_SLAB = 158;
	const WOODEN_SLAB = 158;
	const WOOD_SLABS = 158;
	const WOODEN_SLABS = 158;
	const STAINED_TERRACOTTA = 159;
	const STAINED_HARDENED_CLAY = 159;
	const STAINED_GLASS_PANE = 160;
	const STAINED_GLASS_PANEL = 160;
	const LEAVES2 = 161;
	const WOOD2 = 162;
	const TRUNK2 = 162;
	const LOG2 = 162;
	const ACACIA_WOOD_STAIRS = 163;
	const ACACIA_WOODEN_STAIRS = 163;
	const DARK_OAK_WOOD_STAIRS = 164;
	const DARK_OAK_WOODEN_STAIRS = 164;
	const SLIME_BLOCK = 165;

	const IRON_TRAPDOOR = 167;
	const PRISMARINE = 168;
	const SEA_LANTERN = 169;
	const HAY_BALE = 170;
	const CARPET = 171;
	const TERRACOTTA = 172;
	const COAL_BLOCK = 173;
	const PACKED_ICE = 174;
	const DOUBLE_PLANT = 175;
	const STANDING_BANNER = 176;
	const WALL_BANNER = 177;
	const INVERTED_DAYLIGHT_SENSOR = 178;
	const DAYLIGHT_SENSOR_INVERTED = 178;
	const RED_SANDSTONE = 179;
	const RED_SANDSTONE_STAIRS = 180;
	const DOUBLE_RED_SANDSTONE_SLAB = 181;
	const DOUBLE_PURPUR_SLAB = 181;
	const RED_SANDSTONE_SLAB = 182;
	const PURPUR_SLAB = 182;
	const SPRUCE_FENCE_GATE = 183;
	const FENCE_GATE_SPRUCE = 183;
	const BIRCH_FENCE_GATE = 184;
	const FENCE_GATE_BIRCH = 184;
	const JUNGLE_FENCE_GATE = 185;
	const FENCE_GATE_JUNGLE = 185;
	const DARK_OAK_FENCE_GATE = 186;
	const FENCE_GATE_DARK_OAK = 186;
	const ACACIA_FENCE_GATE = 187;
	const FENCE_GATE_ACACIA = 187;
	const REPEATING_COMMAND_BLOCK = 188;
	const CHAIN_COMMAND_BLOCK = 189;

	const SPRUCE_DOOR_BLOCK = 193;
	const BIRCH_DOOR_BLOCK = 194;
	const JUNGLE_DOOR_BLOCK = 195;
	const ACACIA_DOOR_BLOCK = 196;
	const DARK_OAK_DOOR_BLOCK = 197;
	const GRASS_PATH = 198;
	const ITEM_FRAME_BLOCK = 199;
	const CHORUS_FLOWER = 200;
	const PURPUR = 201;

	const PURPUR_STAIRS = 203;

	const END_STONE_BRICKS = 206;
	const FROSTED_ICE = 207;
	const END_ROD = 208;
	const END_GATEWAY = 209;

	const BONE_BLOCK = 216;
	const SHULKER_BOX = 218;
	const PURPLE_GLAZED_TERRACOTTA = 219;
	const WHITE_GLAZED_TERRACOTTA = 220;
	const ORANGE_GLAZED_TERRACOTTA = 221;
	const MAGENTA_GLAZED_TERRACOTTA = 222;
	const LIGHT_BLUE_GLAZED_TERRACOTTA = 223;
	const YELLOW_GLAZED_TERRACOTTA = 224;
	const LIME_GLAZED_TERRACOTTA = 225;
	const PINK_GLAZED_TERRACOTTA = 226;
	const GRAY_GLAZED_TERRACOTTA = 227;
	const SILVER_GLAZED_TERRACOTTA = 228;
	const CYAN_GLAZED_TERRACOTTA = 229;
	const BLUE_GLAZED_TERRACOTTA = 231;
	const BROWN_GLAZED_TERRACOTTA = 232;
	const GREEN_GLAZED_TERRACOTTA = 233;
	const RED_GLAZED_TERRACOTTA = 234;
	const BLACK_GLAZED_TERRACOTTA = 235;
	const CONCRETE = 236;
	const CONCRETE_POWDER = 237;

	const CHORUS_PLANT = 240;
	const STAINED_GLASS = 241;

	const PODZOL = 243;
	const BEETROOT_BLOCK = 244;
	//const STONECUTTER = 245;
	const GLOWING_OBSIDIAN = 246;
	const NETHER_REACTOR = 247;
	const UPDATE_BLOCK = 248;
	const ATEUPD_BLOCK = 249;
	const BLOCK_MOVED_BY_PISTON = 250;
	const OBSERVER = 251;

	const INFO_RESERVED6 = 255;
	
	const JUKEBOX = 84;
}
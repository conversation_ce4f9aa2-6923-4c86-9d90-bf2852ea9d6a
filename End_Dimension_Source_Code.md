# PocketMine 末地维度相关源代码提取

本文档包含了从 PocketMine 核心中提取的所有与末地维度相关的源代码，包括世界生成、传送、实体、方块、物品等。

## 1. 末地世界生成器 (End World Generator)

### src/pocketmine/level/generator/ender/Ender.php
```php
<?php

namespace pocketmine\level\generator\ender;

use pocketmine\block\Block;
use pocketmine\level\ChunkManager;
use pocketmine\level\generator\biome\Biome;
use pocketmine\level\generator\Generator;
use pocketmine\level\generator\ender\populator\EnderPilar;
use pocketmine\level\generator\noise\Simplex;
use pocketmine\level\generator\populator\Populator;
use pocketmine\math\Vector3;
use pocketmine\utils\Random;

class Ender extends Generator
{
    /** @var Populator[] */
    private $populators = [];
    /** @var ChunkManager */
    private $level;
    /** @var Random */
    private $random;
    private $waterHeight = 0;
    private $emptyHeight = 32;
    private $emptyAmplitude = 1;
    private $density = 0.6;

    /** @var Populator[] */
    private $generationPopulators = [];
    /** @var Simplex */
    private $noiseBase;

    private static $GAUSSIAN_KERNEL = null;
    private static $SMOOTH_SIZE = 2;

    public function __construct(array $options = [])
    {
        if (self::$GAUSSIAN_KERNEL === null) {
            self::generateKernel();
        }
    }

    private static function generateKernel()
    {
        self::$GAUSSIAN_KERNEL = [];
        $bellSize = 1 / self::$SMOOTH_SIZE;
        $bellHeight = 2 * self::$SMOOTH_SIZE;

        for ($sx = -self::$SMOOTH_SIZE; $sx <= self::$SMOOTH_SIZE; ++$sx) {
            self::$GAUSSIAN_KERNEL[$sx + self::$SMOOTH_SIZE] = [];

            for ($sz = -self::$SMOOTH_SIZE; $sz <= self::$SMOOTH_SIZE; ++$sz) {
                $bx = $bellSize * $sx;
                $bz = $bellSize * $sz;
                self::$GAUSSIAN_KERNEL[$sx + self::$SMOOTH_SIZE][$sz + self::$SMOOTH_SIZE] = $bellHeight * exp(-($bx * $bx + $bz * $bz) / 2);
            }
        }
    }

    public function getName(): string
    {
        return "ender";
    }

    public function getSettings(): array
    {
        return [];
    }

    public function init(ChunkManager $level, Random $random)
    {
        $this->level = $level;
        $this->random = $random;
        $this->random->setSeed($this->level->getSeed());
        $this->noiseBase = new Simplex($this->random, 4, 1 / 4, 1 / 64);
        $this->random->setSeed($this->level->getSeed());
        $pilar = new EnderPilar();
        $pilar->setBaseAmount(0);
        $pilar->setRandomAmount(0);
        $this->populators[] = $pilar;
    }

    public function generateChunk($chunkX, $chunkZ)
    {
        $this->random->setSeed(0xa6fe78dc ^ ($chunkX << 8) ^ $chunkZ ^ $this->level->getSeed());

        $noise = Generator::getFastNoise3D($this->noiseBase, 16, 128, 16, 4, 8, 4, $chunkX * 16, 0, $chunkZ * 16);

        $chunk = $this->level->getChunk($chunkX, $chunkZ);

        for ($x = 0; $x < 16; ++$x) {
            for ($z = 0; $z < 16; ++$z) {

                $biome = Biome::getBiome(Biome::END);
                $biome->setGroundCover([
                    Block::get(Block::OBSIDIAN, 0)
                ]);
                $chunk->setBiomeId($x, $z, $biome->getId());
                $color = [0, 0, 0];
                $bColor = 2;
                $color[0] += (($bColor >> 16) ** 2);
                $color[1] += ((($bColor >> 8) & 0xff) ** 2);
                $color[2] += (($bColor & 0xff) ** 2);

                for ($y = 0; $y < 128; ++$y) {
                    $noiseValue = (abs($this->emptyHeight - $y) / $this->emptyHeight) * $this->emptyAmplitude - $noise[$x][$z][$y];
                    $noiseValue -= 1 - $this->density;

                    $distance = new Vector3(0, 64, 0);
                    $distance = $distance->distance(new Vector3($chunkX * 16 + $x, ($y / 1.3), $chunkZ * 16 + $z));

                    if ($noiseValue < 0 && $distance < 100 or $noiseValue < -0.2 && $distance > 400) {
                        $chunk->setBlockId($x, $y, $z, Block::END_STONE);
                    }
                }
            }
        }

        foreach ($this->generationPopulators as $populator) {
            $populator->populate($this->level, $chunkX, $chunkZ, $this->random);
        }
    }

    public function populateChunk($chunkX, $chunkZ)
    {
        $this->random->setSeed(0xa6fe78dc ^ ($chunkX << 8) ^ $chunkZ ^ $this->level->getSeed());
        foreach ($this->populators as $populator) {
            $populator->populate($this->level, $chunkX, $chunkZ, $this->random);
        }

        $chunk = $this->level->getChunk($chunkX, $chunkZ);
        $biome = Biome::getBiome($chunk->getBiomeId(7, 7));
        $biome->populateChunk($this->level, $chunkX, $chunkZ, $this->random);
    }

    public function getSpawn()
    {
        return new Vector3(48, 128, 48);
    }
}
```

### src/pocketmine/level/generator/ender/populator/EnderPilar.php
```php
<?php

namespace pocketmine\level\generator\ender\populator;

use pocketmine\block\Block;
use pocketmine\level\ChunkManager;
use pocketmine\level\generator\populator\Populator;
use pocketmine\utils\Random;

class EnderPilar extends Populator
{
    /** @var ChunkManager */
    private $level;
    private $randomAmount;
    private $baseAmount;

    public function setRandomAmount($amount)
    {
        $this->randomAmount = $amount;
    }

    public function setBaseAmount($amount)
    {
        $this->baseAmount = $amount;
    }

    public function populate(ChunkManager $level, $chunkX, $chunkZ, Random $random)
    {
        if (mt_rand(0, 100) < 10) {
            $this->level = $level;
            $amount = $random->nextRange(0, $this->randomAmount + 1) + $this->baseAmount;
            for ($i = 0; $i < $amount; ++$i) {
                $x = $random->nextRange($chunkX * 16, $chunkX * 16 + 15);
                $z = $random->nextRange($chunkZ * 16, $chunkZ * 16 + 15);
                $y = $this->getHighestWorkableBlock($x, $z);
                if ($this->level->getBlockIdAt($x, $y, $z) == Block::END_STONE) {
                    $height = mt_rand(28, 50);
                    for ($ny = $y; $ny < $y + $height; $ny++) {
                        for ($r = 0.5; $r < 5; $r += 0.5) {
                            $nd = 360 / (2 * pi() * $r);
                            for ($d = 0; $d < 360; $d += $nd) {
                                $level->setBlockIdAt($x + (cos(deg2rad($d)) * $r), $ny, $z + (sin(deg2rad($d)) * $r), Block::OBSIDIAN);
                            }
                        }
                    }
                }
            }
        }
    }

    private function getHighestWorkableBlock($x, $z)
    {
        for ($y = 127; $y >= 0; --$y) {
            $b = $this->level->getBlockIdAt($x, $y, $z);
            if ($b == Block::END_STONE) {
                break;
            }
        }
        return $y === 0 ? -1 : $y;
    }
}
```

### src/pocketmine/level/generator/ender/biome/EnderBiome.php
```php
<?php

namespace pocketmine\level\generator\ender;

use pocketmine\level\generator\biome\Biome;

class EnderBiome extends Biome
{
    public function getName(): string
    {
        return "Ender";
    }
}
```

## 2. 末地相关方块 (End Blocks)

### src/pocketmine/block/EndStone.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Tool;

class EndStone extends Solid {

    protected $id = self::END_STONE;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getName() : string{
        return "End Stone";
    }

    public function getToolType(){
        return Tool::TYPE_PICKAXE;
    }

    public function getHardness(){
        return 3;
    }
}
```

### src/pocketmine/block/EndStoneBricks.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\item\Tool;

class EndStoneBricks extends Solid {

    protected $id = self::END_STONE_BRICKS;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getHardness(){
        return 0.8;
    }

    public function getToolType(){
        return Tool::TYPE_PICKAXE;
    }

    public function getName() : string{
        return "End Stone Bricks";
    }

    public function getDrops(Item $item) : array{
        if($item->isPickaxe() >= 1){
            return [
                [self::END_STONE_BRICKS, $this->meta & 0x03, 1],
            ];
        }else{
            return [];
        }
    }
}
```

### src/pocketmine/block/EndPortal.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;

class EndPortal extends Solid implements SolidLight {

    protected $id = self::END_PORTAL;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getLightLevel(){
        return 1;
    }

    public function getName() : string{
        return "End Portal";
    }

    public function getHardness(){
        return -1;
    }

    public function getResistance(){
        return 18000000;
    }

    public function isBreakable(Item $item){
        return false;
    }

    public function canHarvestWithHand(): bool{
        return false;
    }
}
```

### src/pocketmine/block/EndPortalFrame.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\math\AxisAlignedBB;

class EndPortalFrame extends Solid implements SolidLight {

    protected $id = self::END_PORTAL_FRAME;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getLightLevel(){
        return 1;
    }

    public function getName() : string{
        return "End Portal Frame";
    }

    public function getHardness(){
        return -1;
    }

    public function getResistance(){
        return 18000000;
    }

    public function isBreakable(Item $item){
        return false;
    }

    protected function recalculateBoundingBox(){
        return new AxisAlignedBB(
            $this->x,
            $this->y,
            $this->z,
            $this->x + 1,
            $this->y + (($this->getDamage() & 0x04) > 0 ? 1 : 0.8125),
            $this->z + 1
        );
    }
}
```

### src/pocketmine/block/EndGateway.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\utils\Color;

class EndGateway extends Transparent {

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getName(){
        return "End Gateway";
    }

    public function canPassThrough(){
        return true;
    }

    public function isBreakable(Item $item){
        return false;
    }

    public function getHardness(){
        return -1;
    }

    public function getResistance(){
        return 18000000;
    }

    public function getLightLevel(){
        return 15;
    }

    public function hasEntityCollision(){
        return true;
    }

    public function getColor(){
        return Color::getRGB(0, 0, 0);
    }
}
```

### src/pocketmine/block/EndRod.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\item\Tool;
use pocketmine\math\Vector3;
use pocketmine\Player;

class EndRod extends Flowable implements SolidLight {

    protected $id = self::END_ROD;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getName() : string{
        return "End Rod";
    }

    public function getHardness(){
        return 0;
    }

    public function getResistance(){
        return 0;
    }

    public function getLightLevel(){
        return 14;
    }

    public function getToolType(){
        return Tool::TYPE_PICKAXE;
    }

    public function place(Item $item, Block $block, Block $target, $face, $fx, $fy, $fz, Player $player = null){
        $faces = [
            0 => 0,
            1 => 1,
            2 => 3,
            3 => 2,
            4 => 5,
            5 => 4,
        ];
        $this->meta = ($target->getId() === self::END_ROD && $faces[$face] == $target->getDamage()) ? Vector3::getOppositeSide($faces[$face]) : $faces[$face];
        $this->getLevel()->setBlock($block, $this, true, true);
        return true;
    }

    public function getDrops(Item $item) : array{
        return [
            [$this->id, 0, 1],
        ];
    }
}
```

### src/pocketmine/block/EnderChest.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\item\Tool;
use pocketmine\nbt\tag\CompoundTag;
use pocketmine\nbt\tag\IntTag;
use pocketmine\nbt\tag\StringTag;
use pocketmine\Player;
use pocketmine\tile\EnderChest as TileEnderChest;
use pocketmine\tile\Tile;

class EnderChest extends Transparent {

    protected $id = self::ENDER_CHEST;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getHardness(){
        return 22.5;
    }

    public function getToolType(){
        return Tool::TYPE_PICKAXE;
    }

    public function getName() : string{
        return "Ender Chest";
    }

    public function getLightLevel(){
        return 7;
    }

    public function place(Item $item, Block $block, Block $target, $face, $fx, $fy, $fz, Player $player = null){
        $faces = [
            0 => 4,
            1 => 2,
            2 => 5,
            3 => 3
        ];

        $this->meta = $faces[$player instanceof Player ? $player->getDirection() : 0];

        $this->getLevel()->setBlock($block, $this, true, true);
        $nbt = new CompoundTag("", [
            new StringTag("id", Tile::ENDER_CHEST),
            new IntTag("x", $this->x),
            new IntTag("y", $this->y),
            new IntTag("z", $this->z)
        ]);

        Tile::createTile("EnderChest", $this->getLevel(), $nbt);
        return true;
    }

    public function onActivate(Item $item, Player $player = null){
        if($player instanceof Player){
            $top = $this->getSide(1);
            if($top->isTransparent() !== true){
                return true;
            }

            if(!($this->getLevel()->getTile($this) instanceof TileEnderChest)){
                $nbt = new CompoundTag("", [
                    new StringTag("id", Tile::ENDER_CHEST),
                    new IntTag("x", $this->x),
                    new IntTag("y", $this->y),
                    new IntTag("z", $this->z)
                ]);
                Tile::createTile("EnderChest", $this->getLevel(), $nbt);
            }

            if($player->isCreative() and $player->getServer()->limitedCreative){
                return true;
            }

            $player->getEnderChestInventory()->openAt($this);
        }

        return true;
    }

    public function getDrops(Item $item) : array{
        if($item->isPickaxe() >= 1){
            return [
                [Block::OBSIDIAN, 0, 8],
            ];
        }else{
            return [];
        }
    }
}
```

### src/pocketmine/block/ChorusPlant.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\item\Tool;

class ChorusPlant extends Crops {

    protected $id = self::CHORUS_PLANT;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getHardness(){
        return 0.4;
    }

    public function getToolType(){
        return Tool::TYPE_AXE;
    }

    public function getName() : string{
        return "Chorus Plant";
    }

    public function getDrops(Item $item) : array{
        $drops = [];
        if($this->meta >= 0x07){
            $drops[] = [Item::CHORUS_FRUIT, 0, 1];
        }
        return $drops;
    }
}
```

### src/pocketmine/block/ChorusFlower.php
```php
<?php

namespace pocketmine\block;

use pocketmine\item\Item;
use pocketmine\item\Tool;

class ChorusFlower extends Solid {

    protected $id = self::CHORUS_FLOWER;

    public function __construct($meta = 0){
        $this->meta = $meta;
    }

    public function getHardness(){
        return 0.4;
    }

    public function getToolType(){
        return Tool::TYPE_AXE;
    }

    public function getName() : string{
        return "Chorus Flower";
    }

    public function getDrops(Item $item) : array{
        $drops = [];
        if($this->meta >= 0x07){
            $drops[] = [Item::CHORUS_FRUIT, 0, 1];
        }
        return $drops;
    }
}
```

## 3. 末地相关实体 (End Entities)

### src/pocketmine/entity/EnderDragon.php
```php
<?php

namespace pocketmine\entity;

use pocketmine\network\mcpe\protocol\AddEntityPacket;
use pocketmine\Player;

class EnderDragon extends Monster {

    const NETWORK_ID = 53;

    public $dropExp = [500, 12, 000];//TODO: Add death animation and exp drop.

    public function initEntity(){
        $this->setMaxHealth(200);
        parent::initEntity();
    }

    public function getName() : string{
        return "Ender Dragon";
    }

    public function spawnTo(Player $player){
        $pk = new AddEntityPacket();
        $pk->entityRuntimeId = $this->getId();
        $pk->type = self::NETWORK_ID;
        $pk->position = $this->getPosition();
        $pk->motion = $this->getMotion();
        $pk->yaw = $this->yaw;
        $pk->pitch = $this->pitch;
        $pk->metadata = $this->dataProperties;
        $player->dataPacket($pk);

        parent::spawnTo($player);
    }
}
```

### src/pocketmine/entity/Enderman.php
```php
<?php

namespace pocketmine\entity;

use pocketmine\event\entity\EntityDamageByEntityEvent;
use pocketmine\item\enchantment\Enchantment;
use pocketmine\item\Item as ItemItem;
use pocketmine\network\mcpe\protocol\AddEntityPacket;
use pocketmine\Player;
use pocketmine\entity\behavior\{StrollBehavior, RandomLookaroundBehavior, LookAtPlayerBehavior, PanicBehavior};

class Enderman extends Monster {
    const NETWORK_ID = 38;

    public $width = 0.3;
    public $length = 0.9;
    public $height = 0;
    public $dropExp = [5, 5];
    public $drag = 0.2;
    public $gravity = 0.3;

    public function initEntity(){
        $this->addBehavior(new PanicBehavior($this, 0.25, 2.0));
        $this->addBehavior(new StrollBehavior($this));
        $this->addBehavior(new LookAtPlayerBehavior($this));
        $this->addBehavior(new RandomLookaroundBehavior($this));
        parent::initEntity();
    }

    public function getName() : string{
        return "Enderman";
    }

    public function spawnTo(Player $player){
        $pk = new AddEntityPacket();
        $pk->entityRuntimeId = $this->getId();
        $pk->type = Enderman::NETWORK_ID;
        $pk->position = $this->getPosition();
        $pk->motion = $this->getMotion();
        $pk->yaw = $this->yaw;
        $pk->pitch = $this->pitch;
        $pk->metadata = $this->dataProperties;
        $player->dataPacket($pk);

        parent::spawnTo($player);
    }

    public function getDrops(){
        $cause = $this->lastDamageCause;
        if($cause instanceof EntityDamageByEntityEvent){
            $damager = $cause->getDamager();
            if($damager instanceof Player){
                $lootingL = $damager->getItemInHand()->getEnchantmentLevel(Enchantment::TYPE_WEAPON_LOOTING);
                return [
                    ItemItem::get(ItemItem::ENDER_PEARL, 0, mt_rand(0, 1 + $lootingL))
                ];
            }
        }
        return [
            ItemItem::get(ItemItem::ENDER_PEARL, 0, mt_rand(0, 1))
        ];
    }
}
```

### src/pocketmine/entity/Endermite.php
```php
<?php

namespace pocketmine\entity;

use pocketmine\network\mcpe\protocol\AddEntityPacket;
use pocketmine\Player;

class Endermite extends Monster {
    const NETWORK_ID = 55;

    public $width = 0.3;
    public $length = 0.9;
    public $height = 0;

    public $dropExp = [5, 5];

    public function getName(){
        return "Endermite";
    }

    public function initEntity(){
        $this->setMaxHealth(8);
        parent::initEntity();
    }

    public function spawnTo(Player $player){
        $pk = new AddEntityPacket();
        $pk->entityRuntimeId = $this->getId();
        $pk->type = Endermite::NETWORK_ID;
        $pk->position = $this->getPosition();
        $pk->motion = $this->getMotion();
        $pk->yaw = $this->yaw;
        $pk->pitch = $this->pitch;
        $pk->metadata = $this->dataProperties;
        $player->dataPacket($pk);

        parent::spawnTo($player);
    }
}
```

### src/pocketmine/entity/EnderPearl.php
```php
<?php

namespace pocketmine\entity;

use pocketmine\level\sound\EndermanTeleportSound;
use pocketmine\network\mcpe\protocol\AddEntityPacket;
use pocketmine\Player;

class EnderPearl extends Projectile {
    const NETWORK_ID = 87;

    public $width = 0.25;
    public $length = 0.25;
    public $height = 0.25;

    protected $gravity = 0.03;
    protected $drag = 0.01;

    protected $hasTeleportedShooter = false;

    public function onHit(){
        $this->teleportShooter();
    }

    public function teleportShooter(){
        if(!$this->hasTeleportedShooter){
            $this->hasTeleportedShooter = true;
            if($this->shootingEntity instanceof Player and $this->y > 0){
                $this->shootingEntity->teleport($this->getPosition());
                $this->getLevel()->addSound(new EndermanTeleportSound($this->getPosition()), array($this->shootingEntity));
            }

            $this->kill();
        }
    }

    public function onUpdate($currentTick){
        if($this->closed){
            return false;
        }

        $this->timings->startTiming();

        $hasUpdate = parent::onUpdate($currentTick);

        if($this->age > 1200 or $this->isCollided or $this->hadCollision){
            $this->teleportShooter();
            $hasUpdate = true;
        }

        $this->timings->stopTiming();

        return $hasUpdate;
    }

    public function spawnTo(Player $player){
        $pk = new AddEntityPacket();
        $pk->entityRuntimeId = $this->getId();
        $pk->type = EnderPearl::NETWORK_ID;
        $pk->position = $this->getPosition();
        $pk->motion = $this->getMotion();
        $pk->yaw = $this->yaw;
        $pk->pitch = $this->pitch;
        $pk->metadata = $this->dataProperties;
        $player->dataPacket($pk);

        parent::spawnTo($player);
    }
}
```

## 4. 末地相关物品 (End Items)

### src/pocketmine/item/EyeOfEnder.php
```php
<?php

namespace pocketmine\item;

class EyeOfEnder extends Item {

    public function __construct($meta = 0, $count = 1){
        parent::__construct(self::EYE_OF_ENDER, 0, $count, "Eye Of Ender");
    }
}
```

### src/pocketmine/item/ChorusFruit.php
```php
<?php

namespace pocketmine\item;

use pocketmine\entity\Effect;
use pocketmine\entity\Entity;
use pocketmine\level\sound\EndermanTeleportSound;
use pocketmine\math\Vector3;
use pocketmine\Player;

class ChorusFruit extends Food {

    public function __construct($meta = 0, $count = 1){
        parent::__construct(self::CHORUS_FRUIT, $meta, $count, "Chorus Fruit");
    }

    public function getFoodRestore() : int{
        return 4;
    }

    public function getSaturationRestore() : float{
        return 2.4;
    }

    public function onConsume(Entity $human){
        if($human instanceof Player){
            $minX = $human->getFloorX() - 8;
            $minY = $human->getFloorY() - 8;
            $minZ = $human->getFloorZ() - 8;
            $maxX = $minX + 16;
            $maxY = $minY + 16;
            $maxZ = $minZ + 16;

            for($attempts = 0; $attempts < 16; ++$attempts){
                $x = mt_rand($minX, $maxX);
                $y = mt_rand($minY, $maxY);
                $z = mt_rand($minZ, $maxZ);

                if($y >= 0 and $y < 256){
                    $blockUp = $human->getLevel()->getBlock(new Vector3($x, $y + 1, $z));
                    $blockUp2 = $human->getLevel()->getBlock(new Vector3($x, $y + 2, $z));
                    $blockDown = $human->getLevel()->getBlock(new Vector3($x, $y, $z));

                    if($blockUp->canPassThrough() and $blockUp2->canPassThrough() and !$blockDown->canPassThrough()){
                        $human->teleport(new Vector3($x + 0.5, $y + 1, $z + 0.5));
                        $human->getLevel()->addSound(new EndermanTeleportSound($human->getPosition()));
                        break;
                    }
                }
            }
        }
    }
}
```

## 5. 末地相关声音 (End Sounds)

### src/pocketmine/level/sound/EndermanTeleportSound.php
```php
<?php

namespace pocketmine\level\sound;

use pocketmine\math\Vector3;
use pocketmine\network\mcpe\protocol\LevelEventPacket;

class EndermanTeleportSound extends GenericSound {

    public function __construct(Vector3 $pos){
        parent::__construct($pos, LevelEventPacket::EVENT_SOUND_ENDERMAN_TELEPORT);
    }
}
```

## 6. 维度切换和传送逻辑 (Dimension Switching)

### src/pocketmine/Player.php (相关部分)
```php
// 在 Player 类中的传送门检测方法
public function isInsideOfPortal(){
    $block = $this->level->getBlock($this->floor());
    return $block->getId() === Block::END_PORTAL;
}

// 维度切换方法
public function switchLevel(Level $targetLevel, Vector3 $pos = null){
    if($this->isOnline() and $this->level !== null){
        $this->level->removeEntity($this);
        if($this->chunk !== null){
            $this->chunk->removeEntity($this);
        }
        $this->despawnFromAll();
    }

    $this->setLevel($targetLevel);
    if($pos === null){
        $pos = $targetLevel->getSafeSpawn();
    }
    $this->teleport($pos);

    $this->forceMovement = $pos;
    $this->newPosition = null;

    $this->spawnToAll();

    $pk = new ChangeDimensionPacket();
    $pk->dimension = $targetLevel->getDimension();
    $pk->position = $this->getPosition();
    $this->dataPacket($pk);

    $pk = new PlayStatusPacket();
    $pk->status = PlayStatusPacket::PLAYER_SPAWN;
    $this->dataPacket($pk);
}
```

### src/pocketmine/level/Level.php (相关部分)
```php
// 维度常量定义
const DIMENSION_OVERWORLD = 0;
const DIMENSION_NETHER = 1;
const DIMENSION_END = 2;

// 获取维度方法
public function getDimension(){
    return $this->provider->getDimension();
}
```

## 7. 网络协议包 (Network Packets)

### src/pocketmine/network/mcpe/protocol/ChangeDimensionPacket.php
```php
<?php

namespace pocketmine\network\mcpe\protocol;

use pocketmine\math\Vector3;

class ChangeDimensionPacket extends DataPacket {
    const NETWORK_ID = ProtocolInfo::CHANGE_DIMENSION_PACKET;

    public $dimension;
    public $position;
    public $respawn = false;

    public function decode(){
        $this->dimension = $this->getVarInt();
        $this->position = $this->getVector3();
        $this->respawn = $this->getBool();
    }

    public function encode(){
        $this->reset();
        $this->putVarInt($this->dimension);
        $this->putVector3($this->position);
        $this->putBool($this->respawn);
    }

    public function handle(NetworkSession $session) : bool{
        return $session->handleChangeDimension($this);
    }
}
```

### src/pocketmine/network/mcpe/protocol/PlayStatusPacket.php (相关部分)
```php
<?php

namespace pocketmine\network\mcpe\protocol;

class PlayStatusPacket extends DataPacket {
    const NETWORK_ID = ProtocolInfo::PLAY_STATUS_PACKET;

    const LOGIN_SUCCESS = 0;
    const LOGIN_FAILED_CLIENT = 1;
    const LOGIN_FAILED_SERVER = 2;
    const PLAYER_SPAWN = 3; // 用于加载完成状态
    const LOGIN_FAILED_INVALID_TENANT = 4;
    const LOGIN_FAILED_VANILLA_EDU = 5;
    const LOGIN_FAILED_EDU_VANILLA = 6;

    public $status;

    public function decode(){
        $this->status = $this->getInt();
    }

    public function encode(){
        $this->reset();
        $this->putInt($this->status);
    }

    public function handle(NetworkSession $session) : bool{
        return $session->handlePlayStatus($this);
    }
}
```

## 8. 服务器配置 (Server Configuration)

### src/pocketmine/Server.php (末地相关配置)
```php
// 末地配置属性
public $enderEnabled = true;
public $enderName = "ender";
public $enderLevel = null;

// 加载高级配置
public function loadAdvancedConfig(){
    // ... 其他配置 ...
    $this->enderEnabled = $this->getAdvancedProperty("ender.allow-ender", false);
    $this->enderName = $this->getAdvancedProperty("ender.level-name", "ender");
    // ... 其他配置 ...
}

// 初始化末地世界
public function init(){
    // ... 其他初始化代码 ...

    // 注册末地生成器
    Generator::addGenerator(Ender::class, "ender");

    // ... 其他初始化代码 ...

    // 加载末地世界
    if($this->enderEnabled){
        if(!$this->loadLevel($this->enderName)){
            $this->generateLevel($this->enderName, time(), Generator::getGenerator("ender"));
        }
        $this->enderLevel = $this->getLevelByName($this->enderName);
    }

    // ... 其他初始化代码 ...
}
```

## 9. 生物群系定义 (Biome Definition)

### src/pocketmine/level/generator/biome/Biome.php (末地生物群系常量)
```php
// 生物群系常量
const OCEAN = 0;
const PLAINS = 1;
const DESERT = 2;
const MOUNTAINS = 3;
const FOREST = 4;
const TAIGA = 5;
const SWAMP = 6;
const RIVER = 7;
const HELL = 8;
const END = 9; // 末地生物群系
const FROZEN_OCEAN = 10;
// ... 其他生物群系 ...
```

## 10. 配置文件示例 (Configuration Examples)

### resources/nightmoon_eng.yml (末地配置部分)
```yaml
# 末地配置
ender:
  # 是否启用末地维度
  allow-ender: false
  # 末地世界名称
  level-name: "ender"
```

## 移植说明 (Porting Notes)

### 关键移植要点：

1. **世界生成器**: `Ender.php` 是核心的末地世界生成器，包含地形生成算法
2. **黑曜石柱生成**: `EnderPilar.php` 负责生成末地的黑曜石柱
3. **方块定义**: 所有末地相关方块需要正确的硬度、工具类型和掉落物
4. **实体行为**: 末影人、末影龙等实体的AI行为和属性
5. **传送机制**: 末影珍珠和紫颂果的传送逻辑
6. **维度切换**: `ChangeDimensionPacket` 和相关的维度切换逻辑
7. **加载动画**: `PlayStatusPacket.PLAYER_SPAWN` 用于显示加载完成状态
8. **声音效果**: 末影人传送声音等音效
9. **配置系统**: 服务器配置中的末地启用选项

### Java移植注意事项：

- PHP的数组语法需要转换为Java的List/Array
- PHP的关联数组需要转换为Java的Map
- 注意PHP和Java在数学运算上的差异
- 网络协议包的编码/解码需要适配Nukkit的协议系统
- 实体AI行为系统需要适配Nukkit的实体框架

这个提取包含了PocketMine中所有与末地维度相关的核心代码，可以作为移植到Nukkit的参考基础。
```
